package repository

import (
	"context"
	"sdk-push/model"
	"sdk-push/third_party/yk-push-sdk/push/setting"
	"time"
)

type CommonRepoIface interface {
	Produce(ctx context.Context, payload model.TaskPayload, workTime time.Duration) (err error) // 生产任务，workTime 传了值即为延时任务 不传为软实时任务

	Notify(ctx context.Context, ykAppId string, platform int8, forcedSandbox bool, pushReq *setting.PushMessageRequest) (err error)
	Test(context.Context, model.TestReq) (model.TestResp, error)
	Allow(context.Context, string, int64, int64) bool
	GetUniqueId(ctx context.Context) (string, error)
	//redis
	RedisSet(ctx context.Context, key string, value string, expiration time.Duration) error
	RedisGet(ctx context.Context, key string) (string, error)
	RedisDel(ctx context.Context, key string) error
	// Redis Set operations
	RedisSAdd(ctx context.Context, key string, members ...interface{}) (int64, error)
	RedisSAddWithExpire(ctx context.Context, key string, expiration time.Duration, members ...interface{}) (int64, error)
	RedisSMembers(ctx context.Context, key string) ([]string, error)
	RedisSIsMember(ctx context.Context, key string, member interface{}) (bool, error)
	RedisSRem(ctx context.Context, key string, members ...interface{}) (int64, error)
	RedisSCard(ctx context.Context, key string) (int64, error)
	// Redis pipeline operations for sets
	RedisSAddPipeline(ctx context.Context, key string, expiration time.Duration, members ...interface{}) (int64, error)
}

type ProcRepoIface interface {
	DoSome(ctx context.Context) (err error)
	FindPushToken(ctx context.Context, cond map[string]string) (*model.PushUserParams, error)
	SavePushUserParams(ctx context.Context, params *model.PushUserParams) error
	GetPushMessageRecordWithGroup(ctx context.Context, id int64) (*model.PushMessageRecord, error)
	UpdatePushMessageRecordStatus(ctx context.Context, id uint64, status int) error
	ReadUserIdsFromFile(ctx context.Context, filePath string) (chan string, error)
	QueryUserPlatformDeviceTokens(ctx context.Context, appId uint64, userIds []string, platforms []int8) ([]*model.PushUserParams, error)
	TxUpdatePushCalculation(ctx context.Context, calculation *model.PushCalculation) error
}
