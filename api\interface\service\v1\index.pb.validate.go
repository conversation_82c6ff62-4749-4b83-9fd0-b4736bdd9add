// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/interface/service/v1/index.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CommonRes with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CommonRes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommonRes with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CommonResMultiError, or nil
// if none found.
func (m *CommonRes) ValidateAll() error {
	return m.validate(true)
}

func (m *CommonRes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CommonResValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CommonResValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CommonResValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CommonResMultiError(errors)
	}

	return nil
}

// CommonResMultiError is an error wrapping multiple validation errors returned
// by CommonRes.ValidateAll() if the designated constraints aren't met.
type CommonResMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommonResMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommonResMultiError) AllErrors() []error { return m }

// CommonResValidationError is the validation error returned by
// CommonRes.Validate if the designated constraints aren't met.
type CommonResValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommonResValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommonResValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommonResValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommonResValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommonResValidationError) ErrorName() string { return "CommonResValidationError" }

// Error satisfies the builtin error interface
func (e CommonResValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommonRes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommonResValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommonResValidationError{}

// Validate checks the field values on ExampleReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ExampleReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExampleReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ExampleReqMultiError, or
// nil if none found.
func (m *ExampleReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ExampleReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetAppId() <= 0 {
		err := ExampleReqValidationError{
			field:  "AppId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ChannelId

	// no validation rules for Sign

	if len(errors) > 0 {
		return ExampleReqMultiError(errors)
	}

	return nil
}

// ExampleReqMultiError is an error wrapping multiple validation errors
// returned by ExampleReq.ValidateAll() if the designated constraints aren't met.
type ExampleReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExampleReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExampleReqMultiError) AllErrors() []error { return m }

// ExampleReqValidationError is the validation error returned by
// ExampleReq.Validate if the designated constraints aren't met.
type ExampleReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExampleReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExampleReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExampleReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExampleReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExampleReqValidationError) ErrorName() string { return "ExampleReqValidationError" }

// Error satisfies the builtin error interface
func (e ExampleReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExampleReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExampleReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExampleReqValidationError{}

// Validate checks the field values on ExampleRes with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ExampleRes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExampleRes with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ExampleResMultiError, or
// nil if none found.
func (m *ExampleRes) ValidateAll() error {
	return m.validate(true)
}

func (m *ExampleRes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExampleResValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExampleResValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExampleResValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExampleResMultiError(errors)
	}

	return nil
}

// ExampleResMultiError is an error wrapping multiple validation errors
// returned by ExampleRes.ValidateAll() if the designated constraints aren't met.
type ExampleResMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExampleResMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExampleResMultiError) AllErrors() []error { return m }

// ExampleResValidationError is the validation error returned by
// ExampleRes.Validate if the designated constraints aren't met.
type ExampleResValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExampleResValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExampleResValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExampleResValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExampleResValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExampleResValidationError) ErrorName() string { return "ExampleResValidationError" }

// Error satisfies the builtin error interface
func (e ExampleResValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExampleRes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExampleResValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExampleResValidationError{}

// Validate checks the field values on YourExampleMainData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *YourExampleMainData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on YourExampleMainData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// YourExampleMainDataMultiError, or nil if none found.
func (m *YourExampleMainData) ValidateAll() error {
	return m.validate(true)
}

func (m *YourExampleMainData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SomeData

	if len(errors) > 0 {
		return YourExampleMainDataMultiError(errors)
	}

	return nil
}

// YourExampleMainDataMultiError is an error wrapping multiple validation
// errors returned by YourExampleMainData.ValidateAll() if the designated
// constraints aren't met.
type YourExampleMainDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m YourExampleMainDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m YourExampleMainDataMultiError) AllErrors() []error { return m }

// YourExampleMainDataValidationError is the validation error returned by
// YourExampleMainData.Validate if the designated constraints aren't met.
type YourExampleMainDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e YourExampleMainDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e YourExampleMainDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e YourExampleMainDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e YourExampleMainDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e YourExampleMainDataValidationError) ErrorName() string {
	return "YourExampleMainDataValidationError"
}

// Error satisfies the builtin error interface
func (e YourExampleMainDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sYourExampleMainData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = YourExampleMainDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = YourExampleMainDataValidationError{}

// Validate checks the field values on TestRes with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TestRes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TestRes with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in TestResMultiError, or nil if none found.
func (m *TestRes) ValidateAll() error {
	return m.validate(true)
}

func (m *TestRes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return TestResMultiError(errors)
	}

	return nil
}

// TestResMultiError is an error wrapping multiple validation errors returned
// by TestRes.ValidateAll() if the designated constraints aren't met.
type TestResMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TestResMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TestResMultiError) AllErrors() []error { return m }

// TestResValidationError is the validation error returned by TestRes.Validate
// if the designated constraints aren't met.
type TestResValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TestResValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TestResValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TestResValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TestResValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TestResValidationError) ErrorName() string { return "TestResValidationError" }

// Error satisfies the builtin error interface
func (e TestResValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTestRes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TestResValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TestResValidationError{}

// Validate checks the field values on DevicePushTokenReportReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DevicePushTokenReportReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DevicePushTokenReportReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DevicePushTokenReportReqMultiError, or nil if none found.
func (m *DevicePushTokenReportReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DevicePushTokenReportReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetAppId()) < 1 {
		err := DevicePushTokenReportReqValidationError{
			field:  "AppId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetChannelId()) < 1 {
		err := DevicePushTokenReportReqValidationError{
			field:  "ChannelId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetUserId()) < 1 {
		err := DevicePushTokenReportReqValidationError{
			field:  "UserId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetToken()) < 1 {
		err := DevicePushTokenReportReqValidationError{
			field:  "Token",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetDeviceId()) < 1 {
		err := DevicePushTokenReportReqValidationError{
			field:  "DeviceId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetSOneid()) < 1 {
		err := DevicePushTokenReportReqValidationError{
			field:  "SOneid",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetBundleId()) < 1 {
		err := DevicePushTokenReportReqValidationError{
			field:  "BundleId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _DevicePushTokenReportReq_Platform_InLookup[m.GetPlatform()]; !ok {
		err := DevicePushTokenReportReqValidationError{
			field:  "Platform",
			reason: "value must be in list [1 2 3 4 5 6 7 8]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetSign()) < 1 {
		err := DevicePushTokenReportReqValidationError{
			field:  "Sign",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DevicePushTokenReportReqMultiError(errors)
	}

	return nil
}

// DevicePushTokenReportReqMultiError is an error wrapping multiple validation
// errors returned by DevicePushTokenReportReq.ValidateAll() if the designated
// constraints aren't met.
type DevicePushTokenReportReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DevicePushTokenReportReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DevicePushTokenReportReqMultiError) AllErrors() []error { return m }

// DevicePushTokenReportReqValidationError is the validation error returned by
// DevicePushTokenReportReq.Validate if the designated constraints aren't met.
type DevicePushTokenReportReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DevicePushTokenReportReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DevicePushTokenReportReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DevicePushTokenReportReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DevicePushTokenReportReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DevicePushTokenReportReqValidationError) ErrorName() string {
	return "DevicePushTokenReportReqValidationError"
}

// Error satisfies the builtin error interface
func (e DevicePushTokenReportReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDevicePushTokenReportReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DevicePushTokenReportReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DevicePushTokenReportReqValidationError{}

var _DevicePushTokenReportReq_Platform_InLookup = map[int32]struct{}{
	1: {},
	2: {},
	3: {},
	4: {},
	5: {},
	6: {},
	7: {},
	8: {},
}

// Validate checks the field values on ClientEventReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ClientEventReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClientEventReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ClientEventReqMultiError,
// or nil if none found.
func (m *ClientEventReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ClientEventReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetAppId()) < 1 {
		err := ClientEventReqValidationError{
			field:  "AppId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetChannelId()) < 1 {
		err := ClientEventReqValidationError{
			field:  "ChannelId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetUserId()) < 1 {
		err := ClientEventReqValidationError{
			field:  "UserId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetEventId()) < 1 {
		err := ClientEventReqValidationError{
			field:  "EventId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetDeviceId()) < 1 {
		err := ClientEventReqValidationError{
			field:  "DeviceId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetEventTime() <= 0 {
		err := ClientEventReqValidationError{
			field:  "EventTime",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetMsgId()) < 1 {
		err := ClientEventReqValidationError{
			field:  "MsgId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _ClientEventReq_Platform_InLookup[m.GetPlatform()]; !ok {
		err := ClientEventReqValidationError{
			field:  "Platform",
			reason: "value must be in list [1 2 3 4 5 6 7 8]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ExtraJson

	if utf8.RuneCountInString(m.GetSign()) < 1 {
		err := ClientEventReqValidationError{
			field:  "Sign",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ClientEventReqMultiError(errors)
	}

	return nil
}

// ClientEventReqMultiError is an error wrapping multiple validation errors
// returned by ClientEventReq.ValidateAll() if the designated constraints
// aren't met.
type ClientEventReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClientEventReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClientEventReqMultiError) AllErrors() []error { return m }

// ClientEventReqValidationError is the validation error returned by
// ClientEventReq.Validate if the designated constraints aren't met.
type ClientEventReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClientEventReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClientEventReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClientEventReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClientEventReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClientEventReqValidationError) ErrorName() string { return "ClientEventReqValidationError" }

// Error satisfies the builtin error interface
func (e ClientEventReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClientEventReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClientEventReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClientEventReqValidationError{}

var _ClientEventReq_Platform_InLookup = map[int32]struct{}{
	1: {},
	2: {},
	3: {},
	4: {},
	5: {},
	6: {},
	7: {},
	8: {},
}

// Validate checks the field values on PushWebhookReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PushWebhookReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PushWebhookReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PushWebhookReqMultiError,
// or nil if none found.
func (m *PushWebhookReq) ValidateAll() error {
	return m.validate(true)
}

func (m *PushWebhookReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Platform

	if len(errors) > 0 {
		return PushWebhookReqMultiError(errors)
	}

	return nil
}

// PushWebhookReqMultiError is an error wrapping multiple validation errors
// returned by PushWebhookReq.ValidateAll() if the designated constraints
// aren't met.
type PushWebhookReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PushWebhookReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PushWebhookReqMultiError) AllErrors() []error { return m }

// PushWebhookReqValidationError is the validation error returned by
// PushWebhookReq.Validate if the designated constraints aren't met.
type PushWebhookReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PushWebhookReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PushWebhookReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PushWebhookReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PushWebhookReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PushWebhookReqValidationError) ErrorName() string { return "PushWebhookReqValidationError" }

// Error satisfies the builtin error interface
func (e PushWebhookReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPushWebhookReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PushWebhookReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PushWebhookReqValidationError{}

// Validate checks the field values on UnsafePushReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UnsafePushReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnsafePushReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UnsafePushReqMultiError, or
// nil if none found.
func (m *UnsafePushReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UnsafePushReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetAppId()) < 1 {
		err := UnsafePushReqValidationError{
			field:  "AppId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetChannelId()) < 1 {
		err := UnsafePushReqValidationError{
			field:  "ChannelId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetDeviceId()) < 1 {
		err := UnsafePushReqValidationError{
			field:  "DeviceId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPlatform() <= 0 {
		err := UnsafePushReqValidationError{
			field:  "Platform",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTitle()) < 1 {
		err := UnsafePushReqValidationError{
			field:  "Title",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetContent()) < 1 {
		err := UnsafePushReqValidationError{
			field:  "Content",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetToken()) < 1 {
		err := UnsafePushReqValidationError{
			field:  "Token",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetSign()) < 1 {
		err := UnsafePushReqValidationError{
			field:  "Sign",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UnsafePushReqMultiError(errors)
	}

	return nil
}

// UnsafePushReqMultiError is an error wrapping multiple validation errors
// returned by UnsafePushReq.ValidateAll() if the designated constraints
// aren't met.
type UnsafePushReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnsafePushReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnsafePushReqMultiError) AllErrors() []error { return m }

// UnsafePushReqValidationError is the validation error returned by
// UnsafePushReq.Validate if the designated constraints aren't met.
type UnsafePushReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnsafePushReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnsafePushReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnsafePushReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnsafePushReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnsafePushReqValidationError) ErrorName() string { return "UnsafePushReqValidationError" }

// Error satisfies the builtin error interface
func (e UnsafePushReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnsafePushReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnsafePushReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnsafePushReqValidationError{}

// Validate checks the field values on BenchmarkReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BenchmarkReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BenchmarkReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BenchmarkReqMultiError, or
// nil if none found.
func (m *BenchmarkReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BenchmarkReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetAppId()) < 1 {
		err := BenchmarkReqValidationError{
			field:  "AppId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetSign()) < 1 {
		err := BenchmarkReqValidationError{
			field:  "Sign",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return BenchmarkReqMultiError(errors)
	}

	return nil
}

// BenchmarkReqMultiError is an error wrapping multiple validation errors
// returned by BenchmarkReq.ValidateAll() if the designated constraints aren't met.
type BenchmarkReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BenchmarkReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BenchmarkReqMultiError) AllErrors() []error { return m }

// BenchmarkReqValidationError is the validation error returned by
// BenchmarkReq.Validate if the designated constraints aren't met.
type BenchmarkReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BenchmarkReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BenchmarkReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BenchmarkReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BenchmarkReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BenchmarkReqValidationError) ErrorName() string { return "BenchmarkReqValidationError" }

// Error satisfies the builtin error interface
func (e BenchmarkReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBenchmarkReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BenchmarkReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BenchmarkReqValidationError{}

// Validate checks the field values on CommonRes_Data with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CommonRes_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommonRes_Data with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CommonRes_DataMultiError,
// or nil if none found.
func (m *CommonRes_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *CommonRes_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsOk

	if len(errors) > 0 {
		return CommonRes_DataMultiError(errors)
	}

	return nil
}

// CommonRes_DataMultiError is an error wrapping multiple validation errors
// returned by CommonRes_Data.ValidateAll() if the designated constraints
// aren't met.
type CommonRes_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommonRes_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommonRes_DataMultiError) AllErrors() []error { return m }

// CommonRes_DataValidationError is the validation error returned by
// CommonRes_Data.Validate if the designated constraints aren't met.
type CommonRes_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommonRes_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommonRes_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommonRes_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommonRes_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommonRes_DataValidationError) ErrorName() string { return "CommonRes_DataValidationError" }

// Error satisfies the builtin error interface
func (e CommonRes_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommonRes_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommonRes_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommonRes_DataValidationError{}
