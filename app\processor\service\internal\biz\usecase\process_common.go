package usecase

import (
	"context"
	"fmt"
	"sdk-push/model"
	"sdk-push/third_party/yk-push-sdk/push/setting"
	"strconv"
	"strings"

	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"
)

func (u *Usecase) handleMsg(ctx context.Context, record *model.PushMessageRecord, shouldSend bool) (err error) {
	logCtx := u.log.WithContext(ctx)
	if record == nil {
		logCtx.Warn("[handleMsg] Push message record is empty")
		return
	}
	logCtx.Debugf("[handleMsg] Processing push calculation for record Id: %d", record.Id)
	// 1. 检查推送状态
	if record.PushStatus > model.PushStatusWaiting {
		logCtx.Warnf("[handleMsg] The main push record status is not in waiting status: %d", record.PushStatus)
		return // 非等待状态，直接返回
	}

	// 2.先按类型、渠道分组计算
	var (
		pushSystems = strings.Split(record.PushSystem, ",")
		calculation = &model.PushCalculation{
			MainRecord:        record,
			PlatformPushStats: make(map[int8]*model.PlatformPushStat, len(pushSystems)),
		}
	)
	lo.ForEach(pushSystems, func(system string, _ int) {
		platform, err := strconv.Atoi(system)
		if err != nil {
			return
		}
		calculation.Platforms = append(calculation.Platforms, int8(platform))
		calculation.PlatformPushStats[int8(platform)] = &model.PlatformPushStat{
			PushStatus: model.PushStatusWaiting, // 初始化子记录
		}
	})

	calculation.MainRecord.PushStatus = model.PushStatusWaiting // 主记录状态初始化为等待中
	defer func() {
		// 子记录需要更新状态
		calculation.FinalUpdatePushStatus()
		// 事务更新
		_ = u.procRepo.TxUpdatePushCalculation(ctx, calculation)
	}()

	pushCh, err := u.makeCalculation(ctx, calculation)
	if err != nil {
		return
	}

	// 3. 确定是否发送
	if shouldSend {
		var okNum int
		for platformTokens := range pushCh {
			if err = u.makePush(ctx, calculation, platformTokens); err != nil {
				continue
			}
			okNum++
		}
		calculation.MainRecord.PushStatus = model.PushStatusFailed // 更新主记录状态为失败
		if okNum > 1 {
			calculation.MainRecord.PushStatus = model.PushStatusSuccess // 更新主记录状态为成功
		}
	}
	return
}

// makeCalculation 处理推送对象
func (u *Usecase) makeCalculation(ctx context.Context, calculation *model.PushCalculation) (pushCh chan map[int8][]string, err error) {
	logCtx := u.log.WithContext(ctx)
	pushCh = make(chan map[int8][]string, 1)
	defer close(pushCh)
	// 1. 检查推送状态
	if calculation.MainRecord.PushStatus > model.PushStatusWaiting {
		logCtx.Warnf("[makeCalculation] Push message record status is not waiting: %d", calculation.MainRecord.PushStatus)
		return // 非等待状态，直接返回
	}

	// 2. 如果是人群包
	if calculation.MainRecord.PushRange == model.PushRangeTag {
		if calculation.MainRecord.PushUserGroup == nil {
			logCtx.Errorf("[HandleMsgSending] Push user group not found for record Id: %d", calculation.MainRecord.Id)
			err = fmt.Errorf("push user group not found for record Id: %d", calculation.MainRecord.Id)
			return
		}

		if calculation.MainRecord.PushUserGroup.GroupUploadStatus != model.PushUserGroupStatusSuccess {
			logCtx.Errorf("[HandleMsgSending] Push user group upload status is not success: %d", calculation.MainRecord.PushUserGroup.GroupUploadStatus)
			err = fmt.Errorf("push user group upload status is not success: %d", calculation.MainRecord.PushUserGroup.GroupUploadStatus)
			return
		}
		// 检查人群包文件路径
		if err = u.validateUserGroup(ctx, calculation.MainRecord); err != nil {
			return
		}
		for pushParam := range u.calculateUserGroupPush(ctx, calculation) {
			platformTokens := u.statPushObjects(ctx, calculation, pushParam)
			if len(platformTokens) == 0 {
				continue
			}
			pushCh <- platformTokens
		}
		return
	}
	// 3. 如果是特定token
	if calculation.MainRecord.PushRange == model.PushRangeToken {
		platformTokens := u.calculateTrustlessTokensPush(calculation)
		if len(platformTokens) == 0 {
			return
		}
		pushCh <- platformTokens
		return
	}
	// 4. 如果是指定某userId
	for pushParam := range u.calculateUserIdPush(ctx, calculation) {
		platformTokens := u.statPushObjects(ctx, calculation, pushParam)
		if len(platformTokens) == 0 {
			continue
		}
		pushCh <- platformTokens
	}
	return
}

// statPushObjects 推送数据实际统计环节
func (u *Usecase) statPushObjects(ctx context.Context, calculation *model.PushCalculation, realPushParams []*model.PushUserParams) (platformTokens map[int8][]string) {
	logCtx := u.log.WithContext(ctx)
	if len(realPushParams) == 0 {
		logCtx.Warn("[statPushObjects] No device tokens found for user Ids")
		return // 没有找到token，但不算错误
	}

	// 1. 按平台分组token
	platformTokens = make(map[int8][]string)
	matchedUsers := make(map[int8][]string)
	for _, pushParam := range realPushParams {
		if pushParam.Platform == 0 {
			continue
		}
		if len(pushParam.Token) > 0 {
			platformTokens[pushParam.Platform] = append(platformTokens[pushParam.Platform], pushParam.Token)
		}
		if len(pushParam.UserId) > 0 {
			matchedUsers[pushParam.Platform] = append(matchedUsers[pushParam.Platform], pushParam.UserId)
		}
	}
	// 2. 统计每个平台的推送对象
	for platform, tokenList := range platformTokens {
		tokenCount := len(tokenList)
		if tokenCount == 0 {
			continue
		}
		calculation.IncTotal(platform, tokenCount)                      // 累加总数
		calculation.IncTokenCount(platform, tokenCount)                 // 累加token数
		calculation.IncUserCount(platform, len(matchedUsers[platform])) // 累加有效用户数
	}
	return
}

func (u *Usecase) makePush(ctx context.Context, calculation *model.PushCalculation, platformTokens map[int8][]string) (err error) {
	logCtx := u.log.WithContext(ctx)
	msg := &setting.Message{
		Title:   calculation.MainRecord.PushTitle,
		Content: calculation.MainRecord.PushContent,
	}
	// 1. 为每个平台发送推送
	for platform, tokenList := range platformTokens {
		tokenCount := len(tokenList)
		if tokenCount == 0 {
			continue
		}
		var forcedSandbox bool
		if platform == model.PlatformIOS && calculation.MainRecord.IosSandbox == 0 { // admin设置的ios环境
			forcedSandbox = true
		}
		// 调用推送服务
		appId := strconv.FormatUint(calculation.MainRecord.AppId, 10)
		err = u.commonRepo.Notify(ctx, appId, platform, forcedSandbox, &setting.PushMessageRequest{
			DeviceTokens: tokenList,
			Message:      msg,
		})
		if err == nil {
			logCtx.Infof("[makePush] Successfully pushing on platform %d", platform)
			calculation.IncSentCount(platform, tokenCount)
			continue
		}
		if tokenCount == 1 {
			logCtx.Errorf("[makePush] Failed to push by one token on platform %d: %v", platform, err)
			continue
		}
		// 可能有老鼠屎 按单个重发
		g, gCtx := errgroup.WithContext(ctx)
		for _, token := range tokenList {
			g.Go(func() (_ error) {
				if e := u.commonRepo.Notify(gCtx, appId, platform, forcedSandbox, &setting.PushMessageRequest{DeviceTokens: []string{token}, Message: msg}); e != nil {
					logCtx.Errorf("[makePush] Failed to re-push one token: %s on platform %d: %v", token, platform, e)
					return
				}
				calculation.IncSentCount(platform, 1)
				return
			})
		}
		if err = g.Wait(); err != nil {
			logCtx.Errorf("[makePush] Failed to re-push tokens on platform %d: %v", platform, err)
			continue
		}

		logCtx.Infof("[makePush] Repush succeeded for %d tokens on platform %d, and %d tokens failed. tokens: %+v",
			calculation.GetSentCount(platform), platform, calculation.GetUnSentCount(platform), tokenList)
	}
	return
}
