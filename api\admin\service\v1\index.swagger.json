{"swagger": "2.0", "info": {"title": "api/admin/service/v1/index.proto", "version": "version not set"}, "tags": [{"name": "AdminService"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/admin/v1/push_message/all": {"get": {"summary": "获取模板下拉列表", "operationId": "AdminService_AllPushMessageTemplate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1AllPushMessageTemplateReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "app_id", "description": "应用ID", "in": "query", "required": true, "type": "string", "format": "uint64"}], "tags": ["AdminService"]}}, "/admin/v1/push_message/create": {"post": {"summary": "创建模板", "operationId": "AdminService_CreatePushMessageTemplate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Empty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1CreatePushMessageTemplateRequest"}}], "tags": ["AdminService"]}}, "/admin/v1/push_message/delete": {"post": {"summary": "删除模板", "operationId": "AdminService_DeletePushMessageTemplate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Empty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1DeletePushMessageTemplateRequest"}}], "tags": ["AdminService"]}}, "/admin/v1/push_message/info": {"get": {"summary": "获取模板", "operationId": "AdminService_GetPushMessageTemplate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1PushMessageTemplateDetail"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "query", "required": true, "type": "string", "format": "uint64"}], "tags": ["AdminService"]}}, "/admin/v1/push_message/list": {"get": {"summary": "模板列表", "operationId": "AdminService_ListPushMessageTemplate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1ListPushMessageTemplateReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "page", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "page_size", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "search_text", "description": "搜索标题、内容", "in": "query", "required": false, "type": "string"}, {"name": "push_platform", "description": "推送平台", "in": "query", "required": false, "type": "string"}, {"name": "app_id", "description": "应用ID", "in": "query", "required": true, "type": "string", "format": "uint64"}], "tags": ["AdminService"]}}, "/admin/v1/push_message/update": {"post": {"summary": "更新模板", "operationId": "AdminService_UpdatePushMessageTemplate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Empty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1UpdatePushMessageTemplateRequest"}}], "tags": ["AdminService"]}}, "/admin/v1/push_message_record/cancel": {"post": {"summary": "取消推送", "operationId": "AdminService_CancelPushMessageRecord", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Empty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1CancelPushMessageRecordRequest"}}], "tags": ["AdminService"]}}, "/admin/v1/push_message_record/create": {"post": {"summary": "创建推送记录", "operationId": "AdminService_CreatePushMessageRecord", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Empty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1CreatePushMessageRecordRequest"}}], "tags": ["AdminService"]}}, "/admin/v1/push_message_record/delete": {"post": {"summary": "删除推送记录", "operationId": "AdminService_DeletePushMessageRecord", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Empty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1DeletePushMessageRecordRequest"}}], "tags": ["AdminService"]}}, "/admin/v1/push_message_record/info": {"get": {"summary": "获取推送记录", "operationId": "AdminService_GetPushMessageRecord", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1PushMessageRecordDetail"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "query", "required": true, "type": "string", "format": "uint64"}], "tags": ["AdminService"]}}, "/admin/v1/push_message_record/list": {"get": {"summary": "推送列表", "operationId": "AdminService_ListPushMessageRecord", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1ListPushMessageRecordReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "page", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "page_size", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "search_text", "description": "搜索标题、内容", "in": "query", "required": false, "type": "string"}, {"name": "push_platform", "description": "推送平台", "in": "query", "required": false, "type": "string"}, {"name": "push_status", "description": "推送状态: 1-待推送, 2-推送中, 3-推送成功, 4-推送失败", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "app_id", "description": "应用ID", "in": "query", "required": true, "type": "string", "format": "uint64"}], "tags": ["AdminService"]}}, "/admin/v1/push_user_group/all": {"get": {"summary": "人群包下拉列表", "operationId": "AdminService_AllUserGroup", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1AllPushUserGroupReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "app_id", "description": "应用ID", "in": "query", "required": true, "type": "string", "format": "uint64"}], "tags": ["AdminService"]}}, "/admin/v1/push_user_group/delete": {"post": {"summary": "删除人群包", "operationId": "AdminService_DeleteUserGroup", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1Empty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1DeletePushUserGroupRequest"}}], "tags": ["AdminService"]}}, "/admin/v1/push_user_group/list": {"get": {"summary": "人群包列表", "operationId": "AdminService_ListUserGroup", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1ListPushUserGroupReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "page", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "page_size", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "search_text", "description": "搜索文件名 人群包名称", "in": "query", "required": false, "type": "string"}, {"name": "app_id", "description": "应用ID", "in": "query", "required": true, "type": "string", "format": "uint64"}], "tags": ["AdminService"]}}, "/admin/v1/push_user_group/upload_status": {"get": {"summary": "查询人群包上传状态", "operationId": "AdminService_QueryUserGroupUploadStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1QueryPushUserGroupDownloadStatusReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "query", "required": true, "type": "string", "format": "uint64"}], "tags": ["AdminService"]}}}, "definitions": {"PushMessageRecordChannelDetail": {"type": "object", "properties": {"push_channel": {"type": "integer", "format": "int32", "title": "推送通道"}, "push_channel_icon": {"type": "string", "title": "推送通道icon"}, "user_count": {"type": "string", "format": "uint64", "title": "目标有效用户数"}, "device_count": {"type": "string", "format": "uint64", "title": "目标有效设备数"}, "sent_count": {"type": "string", "format": "uint64", "title": "消息下发数量"}, "sent_rate": {"type": "string", "title": "消息下发成功比"}, "arrival_count": {"type": "string", "format": "uint64", "title": "消息送达数量"}, "arrival_rate": {"type": "string", "title": "消息送达率"}, "display_count": {"type": "string", "format": "uint64", "title": "消息展示数量"}, "display_rate": {"type": "string", "title": "消息展示比"}, "click_count": {"type": "string", "format": "uint64", "title": "消息点击数量"}, "click_rate": {"type": "string", "title": "消息点击率"}, "launch_count": {"type": "string", "format": "uint64", "title": "消息打开数量"}, "launch_rate": {"type": "string", "title": "消息打开率"}, "push_status": {"type": "integer", "format": "int32", "title": "推送状态: 1-待推送, 2-推送中, 3-推送成功, 4-推送失败, 5-已取消"}}}, "googleprotobufAny": {"type": "object", "properties": {"@type": {"type": "string", "description": "A URL/resource name that uniquely identifies the type of the serialized\nprotocol buffer message. This string must contain at least\none \"/\" character. The last segment of the URL's path must represent\nthe fully qualified name of the type (as in\n`path/google.protobuf.Duration`). The name should be in a canonical form\n(e.g., leading \".\" is not accepted).\n\nIn practice, teams usually precompile into the binary all types that they\nexpect it to use in the context of Any. However, for URLs which use the\nscheme `http`, `https`, or no scheme, one can optionally set up a type\nserver that maps type URLs to message definitions as follows:\n\n* If no scheme is provided, `https` is assumed.\n* An HTTP GET on the URL must yield a [google.protobuf.Type][]\n  value in binary format, or produce an error.\n* Applications are allowed to cache lookup results based on the\n  URL, or have them precompiled into a binary to avoid any\n  lookup. Therefore, binary compatibility needs to be preserved\n  on changes to types. (Use versioned type names to manage\n  breaking changes.)\n\nNote: this functionality is not currently available in the official\nprotobuf release, and it is not used for type URLs beginning with\ntype.googleapis.com.\n\nSchemes other than `http`, `https` (or the empty scheme) might be\nused with implementation specific semantics."}}, "additionalProperties": {}, "description": "`Any` contains an arbitrary serialized protocol buffer message along with a\nURL that describes the type of the serialized message.\n\nProtobuf library provides support to pack/unpack Any values in the form\nof utility functions or additional generated methods of the Any type.\n\nExample 1: Pack and unpack a message in C++.\n\n    Foo foo = ...;\n    Any any;\n    any.PackFrom(foo);\n    ...\n    if (any.UnpackTo(&foo)) {\n      ...\n    }\n\nExample 2: Pack and unpack a message in Java.\n\n    Foo foo = ...;\n    Any any = Any.pack(foo);\n    ...\n    if (any.is(Foo.class)) {\n      foo = any.unpack(Foo.class);\n    }\n\nExample 3: Pack and unpack a message in Python.\n\n    foo = Foo(...)\n    any = Any()\n    any.Pack(foo)\n    ...\n    if any.Is(Foo.DESCRIPTOR):\n      any.Unpack(foo)\n      ...\n\nExample 4: Pack and unpack a message in Go\n\n     foo := &pb.Foo{...}\n     any, err := anypb.New(foo)\n     if err != nil {\n       ...\n     }\n     ...\n     foo := &pb.Foo{}\n     if err := any.UnmarshalTo(foo); err != nil {\n       ...\n     }\n\nThe pack methods provided by protobuf library will by default use\n'type.googleapis.com/full.type.name' as the type URL and the unpack\nmethods only use the fully qualified type name after the last '/'\nin the type URL, for example \"foo.bar.com/x/y.z\" will yield type\nname \"y.z\".\n\n\nJSON\n\nThe JSON representation of an `Any` value uses the regular\nrepresentation of the deserialized, embedded message, with an\nadditional field `@type` which contains the type URL. Example:\n\n    package google.profile;\n    message Person {\n      string first_name = 1;\n      string last_name = 2;\n    }\n\n    {\n      \"@type\": \"type.googleapis.com/google.profile.Person\",\n      \"firstName\": <string>,\n      \"lastName\": <string>\n    }\n\nIf the embedded message type is well-known and has a custom JSON\nrepresentation, that representation will be embedded adding a field\n`value` which holds the custom JSON in addition to the `@type`\nfield. Example (for message [google.protobuf.Duration][]):\n\n    {\n      \"@type\": \"type.googleapis.com/google.protobuf.Duration\",\n      \"value\": \"1.212s\"\n    }"}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/googleprotobufAny"}}}}, "v1AllPushMessageTemplateReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/v1PushMessageTemplate"}}}}, "v1AllPushUserGroupReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/v1PushUserGroup"}}}}, "v1CancelPushMessageRecordRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}}, "required": ["id"]}, "v1CreatePushMessageRecordRequest": {"type": "object", "properties": {"push_title": {"type": "string", "title": "推送标题"}, "push_content": {"type": "string", "title": "推送正文"}, "push_platform": {"type": "string", "title": "推送平台"}, "ios_sandbox": {"type": "integer", "format": "int64", "title": "ios推送环境: 0-测试环境, 1-生产环境"}, "push_system": {"type": "string", "title": "推送系统"}, "push_range": {"type": "integer", "format": "int64", "title": "推送范围：0-指定人群包 1-指定用户token 2-指定用户user_id"}, "target_group_id": {"type": "string", "format": "uint64", "title": "目标人群包ID"}, "push_user_tokens": {"type": "string", "title": "指定用户token列表"}, "push_strategy": {"type": "integer", "format": "int64", "title": "推送策略: 0-立即推送, 1-定时推送"}, "scheduled_time": {"type": "string", "title": "计划推送时间"}, "app_id": {"type": "string", "format": "uint64", "title": "应用ID"}, "android_right_pic": {"type": "string", "title": "安卓端右侧小图"}, "android_big_pic": {"type": "string", "title": "安卓端消息大图"}, "ios_big_pic": {"type": "string", "title": "iOS端消息大图"}, "push_user_id": {"type": "string", "title": "指定用户user_id"}}, "title": "推送记录请求结构", "required": ["push_title", "push_content", "push_platform", "push_system", "app_id"]}, "v1CreatePushMessageTemplateRequest": {"type": "object", "properties": {"template_name": {"type": "string", "title": "模板名称"}, "push_title": {"type": "string", "title": "推送标题"}, "push_content": {"type": "string", "title": "推送正文"}, "push_platform": {"type": "string", "title": "推送平台"}, "push_system": {"type": "string", "title": "推送系统"}, "ios_sandbox": {"type": "integer", "format": "int64", "title": "ios推送环境: 0-测试环境, 1-生产环境"}, "app_id": {"type": "string", "format": "uint64", "title": "应用ID"}}, "title": "模板管理请求结构", "required": ["push_title", "push_content", "push_platform", "push_system", "app_id"]}, "v1DeletePushMessageRecordRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}}, "required": ["id"]}, "v1DeletePushMessageTemplateRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}}, "required": ["id"]}, "v1DeletePushUserGroupRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}}, "required": ["id"]}, "v1Empty": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}}}, "v1ListPushMessageRecordReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1ListPushMessageRecordReplyData"}}}, "v1ListPushMessageRecordReplyData": {"type": "object", "properties": {"total": {"type": "string", "format": "uint64"}, "list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/v1PushMessageRecord"}}}}, "v1ListPushMessageTemplateReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1ListPushMessageTemplateReplyData"}}}, "v1ListPushMessageTemplateReplyData": {"type": "object", "properties": {"total": {"type": "string", "format": "uint64"}, "list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/v1PushMessageTemplate"}}}}, "v1ListPushUserGroupReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1ListPushUserGroupReplyData"}}}, "v1ListPushUserGroupReplyData": {"type": "object", "properties": {"total": {"type": "string", "format": "uint64"}, "list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/v1PushUserGroup"}}}}, "v1PushMessageRecord": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "主键ID"}, "push_title": {"type": "string", "title": "推送标题"}, "push_content": {"type": "string", "title": "推送正文"}, "push_system": {"type": "string", "title": "推送系统"}, "target_group_id": {"type": "string", "format": "uint64", "title": "目标人群包ID"}, "push_range": {"type": "integer", "format": "int64", "title": "推送范围：0-指定人群包 1-指定用户token 2-指定用户user_id"}, "push_user_tokens": {"type": "string", "title": "指定用户token"}, "push_status": {"type": "integer", "format": "int64", "title": "推送状态: 1-待推送, 2-推送中, 3-推送成功, 4-推送失败"}, "push_strategy": {"type": "integer", "format": "int64", "title": "推送策略: 0-立即推送, 1-定时推送"}, "scheduled_time": {"type": "string", "title": "计划推送时间"}, "operator": {"type": "string", "title": "最近操作人"}, "created_at": {"type": "string", "title": "创建时间"}, "updated_at": {"type": "string", "title": "更新时间"}, "push_platform": {"type": "string", "title": "推送平台"}, "ios_sandbox": {"type": "integer", "format": "int64", "title": "ios推送环境: 0-测试环境, 1-生产环境"}, "push_user_id": {"type": "string", "title": "指定用户user_id"}, "app_id": {"type": "string", "format": "uint64", "title": "应用ID"}, "android_right_pic": {"type": "string", "title": "安卓端右侧小图"}, "android_big_pic": {"type": "string", "title": "安卓端消息大图"}, "ios_big_pic": {"type": "string", "title": "iOS端消息大图"}, "user_count": {"type": "string", "format": "uint64", "title": "目标有效用户数"}, "device_count": {"type": "string", "format": "uint64", "title": "目标有效设备数"}, "channel_detail": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PushMessageRecordChannelDetail"}, "title": "推送通道详情"}}, "title": "推送消息记录", "required": ["push_title", "push_content"]}, "v1PushMessageRecordDetail": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1PushMessageRecord"}}}, "v1PushMessageTemplate": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "主键ID"}, "template_name": {"type": "string", "title": "模板名称"}, "push_title": {"type": "string", "title": "推送标题"}, "push_content": {"type": "string", "title": "推送正文"}, "push_system": {"type": "string", "title": "推送系统"}, "operator": {"type": "string", "title": "最近操作人"}, "created_at": {"type": "string", "title": "创建时间"}, "updated_at": {"type": "string", "title": "更新时间"}, "push_platform": {"type": "string", "title": "推送平台"}, "ios_sandbox": {"type": "integer", "format": "int64", "title": "ios推送环境: 0-测试环境, 1-生产环境"}, "app_id": {"type": "string", "format": "uint64", "title": "应用ID"}}, "title": "推送消息模板"}, "v1PushMessageTemplateDetail": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1PushMessageTemplate"}}}, "v1PushUserGroup": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "主键ID"}, "group_name": {"type": "string", "title": "人群包名称"}, "user_ids_count": {"type": "string", "format": "uint64", "title": "目标用户ID数量"}, "group_upload_status": {"type": "integer", "format": "int64", "title": "人群包上传状态：1-未上传，2-上传中，3-上传成功，4-上传失败"}, "operator": {"type": "string", "title": "最近操作人"}, "created_at": {"type": "string", "title": "创建时间"}, "updated_at": {"type": "string", "title": "更新时间"}, "app_id": {"type": "string", "format": "uint64", "title": "应用ID"}}, "title": "推送人群包", "required": ["group_name"]}, "v1QueryPushUserGroupDownloadStatusReply": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "data": {"$ref": "#/definitions/v1QueryPushUserGroupDownloadStatusReplyData"}}}, "v1QueryPushUserGroupDownloadStatusReplyData": {"type": "object", "properties": {"upload_status": {"type": "integer", "format": "int64", "title": "1-未上传，2-上传中，3-上传成功，4-上传失败"}, "success_count": {"type": "integer", "format": "int64", "title": "成功数量"}, "fail_count": {"type": "integer", "format": "int64", "title": "失败数量"}, "upload_percent": {"type": "number", "format": "double", "title": "上传百分比，保留2位小数 范围0-1"}}}, "v1UpdatePushMessageTemplateRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "template_name": {"type": "string", "title": "模板名称"}, "push_title": {"type": "string", "title": "推送标题"}, "push_content": {"type": "string", "title": "推送正文"}, "push_platform": {"type": "string", "title": "推送平台"}, "push_system": {"type": "string", "title": "推送系统"}, "ios_sandbox": {"type": "integer", "format": "int64", "title": "ios推送环境: 0-测试环境, 1-生产环境"}}, "required": ["id", "push_title", "push_content", "push_platform", "push_system"]}}}