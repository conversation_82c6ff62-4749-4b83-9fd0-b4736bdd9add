package data

import (
	"context"
	"sdk-push/app/processor/service/internal/biz/repository"
	"sdk-push/model"
	"sdk-push/third_party/yk-push-sdk/push/setting"
	"strconv"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-redis/redis/v8"
	"github.com/hibiken/asynq"
)

type commonRepo struct {
	data *Data
	log  *log.Helper
}

func NewCommonRepo(data *Data, logger log.Logger) repository.CommonRepoIface {
	return &commonRepo{
		data: data,
		log:  log.NewHelper(log.With(logger, "module", "common/repo")),
	}
}

func (d commonRepo) Test(_ context.Context, test model.TestReq) (model.TestResp, error) {
	return model.TestResp{Id: test.Id}, nil
}

func (d commonRepo) Allow(ctx context.Context, key string, limit int64, windowSize int64) (yes bool) {
	now := time.Now().Unix()
	err := d.data.rdb.LPush(ctx, key, now).Err()
	if err != nil {
		d.log.WithContext(ctx).Errorf("[Allow] LPush errs: %v, key: %s", err, key)
		return
	}
	// 设置滑动窗口有效期
	_ = d.data.rdb.Expire(ctx, key, time.Duration(windowSize)*time.Second).Err()
	// 计算list长度
	count, err := d.data.rdb.LLen(ctx, key).Result()
	if err != nil {
		d.log.WithContext(ctx).Errorf("[Allow] LLen errs: %v, key: %s", err, key)
		return
	}
	d.log.WithContext(ctx).Debugf("[Allow] LLen count: %d, limit: %d", count, limit)

	// 每次都会截断list 让list维持某个数量以下
	err = d.data.rdb.LTrim(ctx, key, 0, limit-1).Err()
	if err != nil {
		d.log.WithContext(ctx).Errorf("[Allow] LTrim errs: %v, key: %s", err, key)
		return
	}

	// 获得时间最老的元素
	values, err := d.data.rdb.LRange(ctx, key, -1, -1).Result()
	if err != nil {
		d.log.WithContext(ctx).Errorf("[Allow] LRange errs: %v, key: %s", err, key)
		return
	}

	oldestTimestamp, _ := strconv.ParseInt(values[0], 10, 64)
	d.log.WithContext(ctx).Debugf("[Allow] the oldest time of elememt: %d (s)", oldestTimestamp)

	// 如果最老的元素在窗口内，且 已满，那么拒绝新的元素
	if now-oldestTimestamp < windowSize && count >= limit {
		return
	}

	// 否则允许
	yes = true
	return
}
func (d commonRepo) GetUniqueId(ctx context.Context) (string, error) {
	return d.data.uidCli.GenUniqueID()
}

func (d commonRepo) RedisSet(ctx context.Context, key string, value string, expiration time.Duration) error {
	d.log.WithContext(ctx).Infof("[RedisSet] key: %v,%v", key, value)
	return d.data.rdb.Set(ctx, key, value, expiration).Err()
}

func (d commonRepo) RedisGet(ctx context.Context, key string) (string, error) {
	value, err := d.data.rdb.Get(ctx, key).Result()
	d.log.WithContext(ctx).Infof("[RedisGet] key: %v,%v", key, value)
	return value, err
}

func (d commonRepo) RedisDel(ctx context.Context, key string) error {
	d.log.WithContext(ctx).Infof("[RedisDel] key: %v", key)
	return d.data.rdb.Del(ctx, key).Err()
}

// RedisSAdd adds one or more members to a set stored at key
func (d commonRepo) RedisSAdd(ctx context.Context, key string, members ...interface{}) (int64, error) {
	d.log.WithContext(ctx).Infof("[RedisSAdd] key: %v, members count: %d", key, len(members))
	return d.data.rdb.SAdd(ctx, key, members...).Result()
}

// RedisSAddWithExpire adds one or more members to a set stored at key and sets expiration
func (d commonRepo) RedisSAddWithExpire(ctx context.Context, key string, expiration time.Duration, members ...interface{}) (int64, error) {
	d.log.WithContext(ctx).Infof("[RedisSAddWithExpire] key: %v, expiration: %v, members count: %d", key, expiration, len(members))

	// Add members to the set
	count, err := d.data.rdb.SAdd(ctx, key, members...).Result()
	if err != nil {
		return 0, err
	}

	// Set expiration for the key
	if expiration > 0 {
		if err := d.data.rdb.Expire(ctx, key, expiration).Err(); err != nil {
			d.log.WithContext(ctx).Errorf("[RedisSAddWithExpire] Failed to set expiration for key %s: %v", key, err)
			return count, err
		}
	}

	return count, nil
}

// RedisSMembers returns all members of the set stored at key
func (d commonRepo) RedisSMembers(ctx context.Context, key string) ([]string, error) {
	d.log.WithContext(ctx).Infof("[RedisSMembers] key: %v", key)
	return d.data.rdb.SMembers(ctx, key).Result()
}

// RedisSIsMember checks if member is a member of the set stored at key
func (d commonRepo) RedisSIsMember(ctx context.Context, key string, member interface{}) (bool, error) {
	d.log.WithContext(ctx).Infof("[RedisSIsMember] key: %v, member: %v", key, member)
	return d.data.rdb.SIsMember(ctx, key, member).Result()
}

// RedisSRem removes one or more members from the set stored at key
func (d commonRepo) RedisSRem(ctx context.Context, key string, members ...interface{}) (int64, error) {
	d.log.WithContext(ctx).Infof("[RedisSRem] key: %v, members count: %d", key, len(members))
	return d.data.rdb.SRem(ctx, key, members...).Result()
}

// RedisSCard returns the cardinality (number of elements) of the set stored at key
func (d commonRepo) RedisSCard(ctx context.Context, key string) (int64, error) {
	d.log.WithContext(ctx).Infof("[RedisSCard] key: %v", key)
	return d.data.rdb.SCard(ctx, key).Result()
}

// RedisSAddPipeline adds multiple members to a set using Redis pipeline for better performance
// and sets expiration for the key
func (d commonRepo) RedisSAddPipeline(ctx context.Context, key string, expiration time.Duration, members ...interface{}) (int64, error) {
	if len(members) == 0 {
		return 0, nil
	}

	d.log.WithContext(ctx).Infof("[RedisSAddPipeline] key: %v, expiration: %v, members count: %d", key, expiration, len(members))

	// Create a pipeline
	pipe := d.data.rdb.Pipeline()

	// Add all members to the set in a single pipeline
	cmdAdd := pipe.SAdd(ctx, key, members...)

	// Set expiration if needed
	var cmdExpire *redis.BoolCmd
	if expiration > 0 {
		cmdExpire = pipe.Expire(ctx, key, expiration)
	}

	// Execute the pipeline
	_, err := pipe.Exec(ctx)
	if err != nil {
		d.log.WithContext(ctx).Errorf("[RedisSAddPipeline] Pipeline execution failed: %v", err)
		return 0, err
	}

	// Check for errors in individual commands
	if cmdAdd.Err() != nil {
		d.log.WithContext(ctx).Errorf("[RedisSAddPipeline] SAdd command failed: %v", cmdAdd.Err())
		return 0, cmdAdd.Err()
	}

	if expiration > 0 && cmdExpire.Err() != nil {
		d.log.WithContext(ctx).Errorf("[RedisSAddPipeline] Expire command failed: %v", cmdExpire.Err())
		return cmdAdd.Val(), cmdExpire.Err()
	}

	return cmdAdd.Val(), nil
}

// 配置按ykAppId platform推送
func (d commonRepo) Notify(ctx context.Context, ykAppId string, platform int8, forcedSandbox bool, pushReq *setting.PushMessageRequest) (err error) {
	currentPlatform := model.PlatformSdkMap[platform]
	cli, err := d.data.pushCli.GetPlatformClient(ykAppId, currentPlatform)
	if err != nil {
		d.log.WithContext(ctx).Errorf("[Notify] d.data.pushCli.GetPlatformClient err: %v", err)
		return
	}
	isSandBox := d.data.cfg.GetPush().IsSandBox
	if forcedSandbox {
		isSandBox = true
		d.log.WithContext(ctx).Infof("[Notify] forcedSandbox: %v", forcedSandbox)
	}
	pushReq.Message.BusinessId = d.GenYokaMsgId(ctx)
	pushReq.IsSandBox = isSandBox
	d.log.WithContext(ctx).Infof("[Notify] pushReq: %+v", pushReq)
	respPush, err := cli.PushNotice(ctx, pushReq)
	if err != nil {
		d.log.WithContext(ctx).Errorf("[Notify] cli.PushNotice err: %v with pushReq: %+v", err, pushReq)
		return
	}
	d.log.WithContext(ctx).Infof("[Notify] respPush result: %+v", respPush)
	return
}

func (d commonRepo) GenYokaMsgId(ctx context.Context) (msgId string) {
	msgId, err := d.data.msgIdCli.GenMsgID()
	if err != nil {
		d.log.WithContext(ctx).Errorf("[GenYokaMsgId] err: %v", err)
		return
	}
	d.log.WithContext(ctx).Debugf("[GenYokaMsgId] msgId: %v", msgId)
	return
}

// Produce workTime 传了值即为延时任务 不传为软实时任务
func (d commonRepo) Produce(ctx context.Context, payload model.TaskPayload, workTime time.Duration) (err error) {
	var opts []asynq.Option
	if workTime.Seconds() > 0 {
		d.log.WithContext(ctx).Infof("[Produce] task will be processed after %d", workTime.Seconds())
		opts = append(opts, asynq.ProcessIn(workTime))
	}
	opts = append(opts, asynq.Retention(3*time.Hour)) // 设置任务的保留期限为3小时，以便在任务完成后进行清理(方便查问题)。
	_, err = d.data.producer.Enqueue(model.NewConsumeTask(payload), opts...)
	if err != nil {
		d.log.WithContext(ctx).Errorf("[Produce] payload: %v, err: %v", payload, err)
		return err
	}
	d.log.WithContext(ctx).Debugf("[Produce] payload: %v", payload)
	return nil
}
