{"swagger": "2.0", "info": {"title": "api/interface/service/v1/index.proto", "version": "version not set"}, "tags": [{"name": "interface"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/benchmark": {"post": {"operationId": "interface_Benchmark", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1CommonRes"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1BenchmarkReq"}}], "tags": ["interface"]}}, "/api/v1/event": {"post": {"operationId": "interface_ClientEvent", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1CommonRes"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1ClientEventReq"}}], "tags": ["interface"]}}, "/api/v1/push_webhook/{id}/{platform}": {"get": {"operationId": "interface_PushWebhook2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1CommonRes"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "platform", "in": "path", "required": true, "type": "integer", "format": "int32"}], "tags": ["interface"]}, "post": {"operationId": "interface_PushWebhook", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1CommonRes"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "int64"}, {"name": "platform", "in": "path", "required": true, "type": "integer", "format": "int32"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interfacePushWebhookBody"}}], "tags": ["interface"]}}, "/api/v1/report": {"post": {"operationId": "interface_DevicePushTokenReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1CommonRes"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1DevicePushTokenReportReq"}}], "tags": ["interface"]}}, "/api/v1/unsafe_push": {"post": {"operationId": "interface_UnsafePush", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1CommonRes"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1UnsafePushReq"}}], "tags": ["interface"]}}, "/health": {"get": {"operationId": "interface_Health2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1CommonRes"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["interface"]}, "post": {"operationId": "interface_Health", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1CommonRes"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {}}}], "tags": ["interface"]}}}, "definitions": {"CommonResData": {"type": "object", "properties": {"is_ok": {"type": "boolean"}}}, "interfacePushWebhookBody": {"type": "object"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "v1BenchmarkReq": {"type": "object", "properties": {"app_id": {"type": "string", "title": "app_id 与 签名要匹配"}, "sign": {"type": "string", "title": "签名"}}, "required": ["app_id", "sign"]}, "v1ClientEventReq": {"type": "object", "properties": {"app_id": {"type": "string", "title": "应用id"}, "channel_id": {"type": "string", "title": "渠道id"}, "user_id": {"type": "string", "title": "用户id"}, "event_id": {"type": "string", "title": "魔方的事件id"}, "device_id": {"type": "string", "title": "设备id"}, "event_time": {"type": "string", "format": "uint64", "title": "事件触发的时间 秒级时间戳"}, "msg_id": {"type": "string", "title": "消息id 通过消息通知透传字段获取"}, "platform": {"type": "integer", "format": "int32", "title": "推送平台类型 1:ios-官推 2:华为-官推 3:荣耀-官推 4:小米-官推 5:oppo-官推 6:vivo-官推 7:魅族-官推 8:三方推送-极光平台（自适应全手机平台）"}, "extra_json": {"type": "string", "title": "可选 额外的json字符串数据 移动端需要将数据序列化为字符串再赋值给该字段"}, "sign": {"type": "string", "title": "签名"}}, "required": ["app_id", "channel_id", "user_id", "event_id", "device_id", "event_time", "msg_id", "platform", "sign"]}, "v1CommonRes": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/CommonResData"}}, "title": "公共响应体"}, "v1DevicePushTokenReportReq": {"type": "object", "properties": {"app_id": {"type": "string", "title": "应用id"}, "channel_id": {"type": "string", "title": "渠道id"}, "user_id": {"type": "string", "title": "用户id"}, "token": {"type": "string", "title": "设备推送token"}, "device_id": {"type": "string", "title": "设备id"}, "s_oneid": {"type": "string", "title": "设备指纹id"}, "bundle_id": {"type": "string", "title": "包名"}, "platform": {"type": "integer", "format": "int32", "title": "推送平台类型 1:ios-官推 2:华为-官推 3:荣耀-官推 4:小米-官推 5:oppo-官推 6:vivo-官推 7:魅族-官推 8:三方推送-极光平台（自适应全手机平台）"}, "sign": {"type": "string", "title": "签名"}}, "title": "enum Plartform {\n  UNSPECIFIED = 0; // 无含义\n  IOS = 1; // iOS-官方推送\n  HUAWEI = 2; // 华为-官方推送\n  HONOR = 3; // 荣耀-官方推送\n  XIAOMI = 4; // 小米-官方推送\n  OPPO = 5; // OPPO-官方推送\n  VIVO = 6; // vivo-官方推送\n  MEIZU = 7; // 魅族-官方推送\n  JPUSH = 8; // 三方推送-极光平台（自适应全手机平台）\n}", "required": ["app_id", "channel_id", "user_id", "token", "device_id", "s_oneid", "bundle_id", "platform", "sign"]}, "v1UnsafePushReq": {"type": "object", "properties": {"app_id": {"type": "string"}, "channel_id": {"type": "string"}, "device_id": {"type": "string"}, "platform": {"type": "integer", "format": "int32"}, "title": {"type": "string"}, "content": {"type": "string"}, "token": {"type": "string", "title": "推送要用的token 需要是推送平台存储过的"}, "sign": {"type": "string", "title": "签名"}}, "required": ["app_id", "channel_id", "device_id", "platform", "title", "content", "token", "sign"]}}}