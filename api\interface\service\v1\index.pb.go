// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v3.10.1
// source: api/interface/service/v1/index.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 公共响应体
type CommonRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32           `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Data *CommonRes_Data `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CommonRes) Reset() {
	*x = CommonRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_interface_service_v1_index_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonRes) ProtoMessage() {}

func (x *CommonRes) ProtoReflect() protoreflect.Message {
	mi := &file_api_interface_service_v1_index_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonRes.ProtoReflect.Descriptor instead.
func (*CommonRes) Descriptor() ([]byte, []int) {
	return file_api_interface_service_v1_index_proto_rawDescGZIP(), []int{0}
}

func (x *CommonRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CommonRes) GetData() *CommonRes_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

// 具体字段对外使用下划线json格式 在golang技术栈内部会自动生成驼峰struct 迁移过来的老协议考虑兼容 不遵循该规则
type ExampleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     int32  `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	ChannelId int64  `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Sign      string `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign,omitempty"`
}

func (x *ExampleReq) Reset() {
	*x = ExampleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_interface_service_v1_index_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExampleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExampleReq) ProtoMessage() {}

func (x *ExampleReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_interface_service_v1_index_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExampleReq.ProtoReflect.Descriptor instead.
func (*ExampleReq) Descriptor() ([]byte, []int) {
	return file_api_interface_service_v1_index_proto_rawDescGZIP(), []int{1}
}

func (x *ExampleReq) GetAppId() int32 {
	if x != nil {
		return x.AppId
	}
	return 0
}

func (x *ExampleReq) GetChannelId() int64 {
	if x != nil {
		return x.ChannelId
	}
	return 0
}

func (x *ExampleReq) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

type ExampleRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// code
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// data
	Data *YourExampleMainData `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ExampleRes) Reset() {
	*x = ExampleRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_interface_service_v1_index_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExampleRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExampleRes) ProtoMessage() {}

func (x *ExampleRes) ProtoReflect() protoreflect.Message {
	mi := &file_api_interface_service_v1_index_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExampleRes.ProtoReflect.Descriptor instead.
func (*ExampleRes) Descriptor() ([]byte, []int) {
	return file_api_interface_service_v1_index_proto_rawDescGZIP(), []int{2}
}

func (x *ExampleRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ExampleRes) GetData() *YourExampleMainData {
	if x != nil {
		return x.Data
	}
	return nil
}

type YourExampleMainData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SomeData string `protobuf:"bytes,1,opt,name=some_data,json=someData,proto3" json:"some_data,omitempty"`
}

func (x *YourExampleMainData) Reset() {
	*x = YourExampleMainData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_interface_service_v1_index_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *YourExampleMainData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YourExampleMainData) ProtoMessage() {}

func (x *YourExampleMainData) ProtoReflect() protoreflect.Message {
	mi := &file_api_interface_service_v1_index_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YourExampleMainData.ProtoReflect.Descriptor instead.
func (*YourExampleMainData) Descriptor() ([]byte, []int) {
	return file_api_interface_service_v1_index_proto_rawDescGZIP(), []int{3}
}

func (x *YourExampleMainData) GetSomeData() string {
	if x != nil {
		return x.SomeData
	}
	return ""
}

type TestRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *TestRes) Reset() {
	*x = TestRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_interface_service_v1_index_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRes) ProtoMessage() {}

func (x *TestRes) ProtoReflect() protoreflect.Message {
	mi := &file_api_interface_service_v1_index_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRes.ProtoReflect.Descriptor instead.
func (*TestRes) Descriptor() ([]byte, []int) {
	return file_api_interface_service_v1_index_proto_rawDescGZIP(), []int{4}
}

func (x *TestRes) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

//	enum Plartform {
//	  UNSPECIFIED = 0; // 无含义
//	  IOS = 1; // iOS-官方推送
//	  HUAWEI = 2; // 华为-官方推送
//	  HONOR = 3; // 荣耀-官方推送
//	  XIAOMI = 4; // 小米-官方推送
//	  OPPO = 5; // OPPO-官方推送
//	  VIVO = 6; // vivo-官方推送
//	  MEIZU = 7; // 魅族-官方推送
//	  JPUSH = 8; // 三方推送-极光平台（自适应全手机平台）
//	}
type DevicePushTokenReportReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`             // 应用id
	ChannelId string `protobuf:"bytes,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"` // 渠道id
	UserId    string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`          // 用户id
	Token     string `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`                          // 设备推送token
	DeviceId  string `protobuf:"bytes,5,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`    // 设备id
	SOneid    string `protobuf:"bytes,6,opt,name=s_oneid,json=sOneid,proto3" json:"s_oneid,omitempty"`          // 设备指纹id
	BundleId  string `protobuf:"bytes,7,opt,name=bundle_id,json=bundleId,proto3" json:"bundle_id,omitempty"`    // 包名
	Platform  int32  `protobuf:"varint,8,opt,name=platform,proto3" json:"platform,omitempty"`                   // 推送平台类型 1:ios-官推 2:华为-官推 3:荣耀-官推 4:小米-官推 5:oppo-官推 6:vivo-官推 7:魅族-官推 8:三方推送-极光平台（自适应全手机平台）
	Sign      string `protobuf:"bytes,9,opt,name=sign,proto3" json:"sign,omitempty"`                            // 签名
}

func (x *DevicePushTokenReportReq) Reset() {
	*x = DevicePushTokenReportReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_interface_service_v1_index_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevicePushTokenReportReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevicePushTokenReportReq) ProtoMessage() {}

func (x *DevicePushTokenReportReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_interface_service_v1_index_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevicePushTokenReportReq.ProtoReflect.Descriptor instead.
func (*DevicePushTokenReportReq) Descriptor() ([]byte, []int) {
	return file_api_interface_service_v1_index_proto_rawDescGZIP(), []int{5}
}

func (x *DevicePushTokenReportReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *DevicePushTokenReportReq) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *DevicePushTokenReportReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *DevicePushTokenReportReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *DevicePushTokenReportReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *DevicePushTokenReportReq) GetSOneid() string {
	if x != nil {
		return x.SOneid
	}
	return ""
}

func (x *DevicePushTokenReportReq) GetBundleId() string {
	if x != nil {
		return x.BundleId
	}
	return ""
}

func (x *DevicePushTokenReportReq) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *DevicePushTokenReportReq) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

type ClientEventReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`              // 应用id
	ChannelId string `protobuf:"bytes,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`  // 渠道id
	UserId    string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`           // 用户id
	EventId   string `protobuf:"bytes,4,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty"`        // 魔方的事件id
	DeviceId  string `protobuf:"bytes,5,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`     // 设备id
	EventTime uint64 `protobuf:"varint,6,opt,name=event_time,json=eventTime,proto3" json:"event_time,omitempty"` // 事件触发的时间 秒级时间戳
	MsgId     string `protobuf:"bytes,7,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`              // 消息id 通过消息通知透传字段获取
	Platform  int32  `protobuf:"varint,8,opt,name=platform,proto3" json:"platform,omitempty"`                    // 推送平台类型 1:ios-官推 2:华为-官推 3:荣耀-官推 4:小米-官推 5:oppo-官推 6:vivo-官推 7:魅族-官推 8:三方推送-极光平台（自适应全手机平台）
	ExtraJson string `protobuf:"bytes,9,opt,name=extra_json,json=extraJson,proto3" json:"extra_json,omitempty"`  // 可选 额外的json字符串数据 移动端需要将数据序列化为字符串再赋值给该字段
	Sign      string `protobuf:"bytes,10,opt,name=sign,proto3" json:"sign,omitempty"`                            // 签名
}

func (x *ClientEventReq) Reset() {
	*x = ClientEventReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_interface_service_v1_index_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientEventReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientEventReq) ProtoMessage() {}

func (x *ClientEventReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_interface_service_v1_index_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientEventReq.ProtoReflect.Descriptor instead.
func (*ClientEventReq) Descriptor() ([]byte, []int) {
	return file_api_interface_service_v1_index_proto_rawDescGZIP(), []int{6}
}

func (x *ClientEventReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ClientEventReq) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *ClientEventReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ClientEventReq) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *ClientEventReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *ClientEventReq) GetEventTime() uint64 {
	if x != nil {
		return x.EventTime
	}
	return 0
}

func (x *ClientEventReq) GetMsgId() string {
	if x != nil {
		return x.MsgId
	}
	return ""
}

func (x *ClientEventReq) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *ClientEventReq) GetExtraJson() string {
	if x != nil {
		return x.ExtraJson
	}
	return ""
}

func (x *ClientEventReq) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

type PushWebhookReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Platform int32 `protobuf:"varint,2,opt,name=platform,proto3" json:"platform,omitempty"`
}

func (x *PushWebhookReq) Reset() {
	*x = PushWebhookReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_interface_service_v1_index_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushWebhookReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushWebhookReq) ProtoMessage() {}

func (x *PushWebhookReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_interface_service_v1_index_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushWebhookReq.ProtoReflect.Descriptor instead.
func (*PushWebhookReq) Descriptor() ([]byte, []int) {
	return file_api_interface_service_v1_index_proto_rawDescGZIP(), []int{7}
}

func (x *PushWebhookReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PushWebhookReq) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

type UnsafePushReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	ChannelId string `protobuf:"bytes,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DeviceId  string `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Platform  int32  `protobuf:"varint,4,opt,name=platform,proto3" json:"platform,omitempty"`
	Title     string `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	Content   string `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`
	Token     string `protobuf:"bytes,7,opt,name=token,proto3" json:"token,omitempty"` // 推送要用的token 需要是推送平台存储过的
	Sign      string `protobuf:"bytes,8,opt,name=sign,proto3" json:"sign,omitempty"`   // 签名
}

func (x *UnsafePushReq) Reset() {
	*x = UnsafePushReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_interface_service_v1_index_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnsafePushReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsafePushReq) ProtoMessage() {}

func (x *UnsafePushReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_interface_service_v1_index_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsafePushReq.ProtoReflect.Descriptor instead.
func (*UnsafePushReq) Descriptor() ([]byte, []int) {
	return file_api_interface_service_v1_index_proto_rawDescGZIP(), []int{8}
}

func (x *UnsafePushReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *UnsafePushReq) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *UnsafePushReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *UnsafePushReq) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *UnsafePushReq) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UnsafePushReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *UnsafePushReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *UnsafePushReq) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

type BenchmarkReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"` // app_id 与 签名要匹配
	Sign  string `protobuf:"bytes,2,opt,name=sign,proto3" json:"sign,omitempty"`                // 签名
}

func (x *BenchmarkReq) Reset() {
	*x = BenchmarkReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_interface_service_v1_index_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BenchmarkReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BenchmarkReq) ProtoMessage() {}

func (x *BenchmarkReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_interface_service_v1_index_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BenchmarkReq.ProtoReflect.Descriptor instead.
func (*BenchmarkReq) Descriptor() ([]byte, []int) {
	return file_api_interface_service_v1_index_proto_rawDescGZIP(), []int{9}
}

func (x *BenchmarkReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BenchmarkReq) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

type CommonRes_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsOk bool `protobuf:"varint,1,opt,name=is_ok,json=isOk,proto3" json:"is_ok,omitempty"`
}

func (x *CommonRes_Data) Reset() {
	*x = CommonRes_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_interface_service_v1_index_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonRes_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonRes_Data) ProtoMessage() {}

func (x *CommonRes_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_interface_service_v1_index_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonRes_Data.ProtoReflect.Descriptor instead.
func (*CommonRes_Data) Descriptor() ([]byte, []int) {
	return file_api_interface_service_v1_index_proto_rawDescGZIP(), []int{0, 0}
}

func (x *CommonRes_Data) GetIsOk() bool {
	if x != nil {
		return x.IsOk
	}
	return false
}

var File_api_interface_service_v1_index_proto protoreflect.FileDescriptor

var file_api_interface_service_v1_index_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x70, 0x0a, 0x09, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x32, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x1a, 0x1b, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x13, 0x0a, 0x05, 0x69, 0x73, 0x5f, 0x6f,
	0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x69, 0x73, 0x4f, 0x6b, 0x22, 0x63, 0x0a,
	0x0a, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x06, 0x61,
	0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0xe2, 0x41, 0x01,
	0x02, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69,
	0x67, 0x6e, 0x22, 0x59, 0x0a, 0x0a, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x59, 0x6f, 0x75, 0x72, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x4d,
	0x61, 0x69, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x32, 0x0a,
	0x13, 0x59, 0x6f, 0x75, 0x72, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x4d, 0x61, 0x69, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x6d, 0x65, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x6d, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x22, 0x19, 0x0a, 0x07, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x85, 0x03, 0x0a,
	0x18, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x75, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x06, 0x61, 0x70, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x2a, 0x0a,
	0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xe2, 0x41, 0x01, 0x02,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b,
	0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x28, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x07,
	0x73, 0x5f, 0x6f, 0x6e, 0x65, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xe2,
	0x41, 0x01, 0x02, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x4f, 0x6e, 0x65,
	0x69, 0x64, 0x12, 0x28, 0x0a, 0x09, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x08, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x08,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x42, 0x19,
	0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42, 0x12, 0x1a, 0x10, 0x30, 0x01, 0x30, 0x02, 0x30, 0x03, 0x30,
	0x04, 0x30, 0x05, 0x30, 0x06, 0x30, 0x07, 0x30, 0x08, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x12, 0x1f, 0x0a, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04,
	0x73, 0x69, 0x67, 0x6e, 0x22, 0x9f, 0x03, 0x0a, 0x0e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x0a, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a,
	0x08, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x2a, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x04, 0x42, 0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x06, 0x6d,
	0x73, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xe2, 0x41, 0x01,
	0x02, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x12,
	0x35, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x19, 0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42, 0x12, 0x1a, 0x10, 0x30, 0x01, 0x30, 0x02,
	0x30, 0x03, 0x30, 0x04, 0x30, 0x05, 0x30, 0x06, 0x30, 0x07, 0x30, 0x08, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f,
	0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x22, 0x3c, 0x0a, 0x0e, 0x50, 0x75, 0x73, 0x68, 0x57, 0x65,
	0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x22, 0xc0, 0x02, 0x0a, 0x0d, 0x55, 0x6e, 0x73, 0x61, 0x66, 0x65, 0x50,
	0x75, 0x73, 0x68, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x0a, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b,
	0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x27, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52,
	0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x25, 0x0a, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xe2,
	0x41, 0x01, 0x02, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f, 0x0a, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x22, 0x53, 0x0a, 0x0c, 0x42, 0x65, 0x6e, 0x63, 0x68,
	0x6d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x04, 0x73,
	0x69, 0x67, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0xe2, 0x41, 0x01, 0x02, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x32, 0xb4, 0x05, 0x0a,
	0x09, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x12, 0x5a, 0x0a, 0x06, 0x48, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x3a,
	0x01, 0x2a, 0x5a, 0x09, 0x12, 0x07, 0x2f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x22, 0x07, 0x2f,
	0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x12, 0x77, 0x0a, 0x15, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x50, 0x75, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12,
	0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x75, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x3a, 0x01, 0x2a, 0x22,
	0x0e, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12,
	0x62, 0x0a, 0x0b, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x1e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x19,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x12, 0x3a, 0x01, 0x2a, 0x22, 0x0d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x12, 0x66, 0x0a, 0x0a, 0x55, 0x6e, 0x73, 0x61, 0x66, 0x65, 0x50, 0x75, 0x73,
	0x68, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x6e, 0x73, 0x61, 0x66, 0x65, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x71,
	0x1a, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x22, 0x1e, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x18, 0x3a, 0x01, 0x2a, 0x22, 0x13, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f,
	0x75, 0x6e, 0x73, 0x61, 0x66, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x12, 0xa1, 0x01, 0x0a, 0x0b,
	0x50, 0x75, 0x73, 0x68, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x12, 0x1e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73,
	0x68, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x22, 0x57, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x51, 0x3a, 0x01,
	0x2a, 0x5a, 0x26, 0x12, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x75, 0x73,
	0x68, 0x5f, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x7b,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x7d, 0x22, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x2f,
	0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x7b, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x7d, 0x12,
	0x62, 0x0a, 0x09, 0x42, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x1c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x65,
	0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x3a, 0x01, 0x2a,
	0x22, 0x11, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x65, 0x6e, 0x63, 0x68, 0x6d,
	0x61, 0x72, 0x6b, 0x42, 0x22, 0x5a, 0x20, 0x73, 0x64, 0x6b, 0x2d, 0x70, 0x75, 0x73, 0x68, 0x2f,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_interface_service_v1_index_proto_rawDescOnce sync.Once
	file_api_interface_service_v1_index_proto_rawDescData = file_api_interface_service_v1_index_proto_rawDesc
)

func file_api_interface_service_v1_index_proto_rawDescGZIP() []byte {
	file_api_interface_service_v1_index_proto_rawDescOnce.Do(func() {
		file_api_interface_service_v1_index_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_interface_service_v1_index_proto_rawDescData)
	})
	return file_api_interface_service_v1_index_proto_rawDescData
}

var file_api_interface_service_v1_index_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_api_interface_service_v1_index_proto_goTypes = []any{
	(*CommonRes)(nil),                // 0: api.service.v1.CommonRes
	(*ExampleReq)(nil),               // 1: api.service.v1.ExampleReq
	(*ExampleRes)(nil),               // 2: api.service.v1.ExampleRes
	(*YourExampleMainData)(nil),      // 3: api.service.v1.YourExampleMainData
	(*TestRes)(nil),                  // 4: api.service.v1.TestRes
	(*DevicePushTokenReportReq)(nil), // 5: api.service.v1.DevicePushTokenReportReq
	(*ClientEventReq)(nil),           // 6: api.service.v1.ClientEventReq
	(*PushWebhookReq)(nil),           // 7: api.service.v1.PushWebhookReq
	(*UnsafePushReq)(nil),            // 8: api.service.v1.UnsafePushReq
	(*BenchmarkReq)(nil),             // 9: api.service.v1.BenchmarkReq
	(*CommonRes_Data)(nil),           // 10: api.service.v1.CommonRes.Data
	(*emptypb.Empty)(nil),            // 11: google.protobuf.Empty
}
var file_api_interface_service_v1_index_proto_depIdxs = []int32{
	10, // 0: api.service.v1.CommonRes.data:type_name -> api.service.v1.CommonRes.Data
	3,  // 1: api.service.v1.ExampleRes.data:type_name -> api.service.v1.YourExampleMainData
	11, // 2: api.service.v1.interface.Health:input_type -> google.protobuf.Empty
	5,  // 3: api.service.v1.interface.DevicePushTokenReport:input_type -> api.service.v1.DevicePushTokenReportReq
	6,  // 4: api.service.v1.interface.ClientEvent:input_type -> api.service.v1.ClientEventReq
	8,  // 5: api.service.v1.interface.UnsafePush:input_type -> api.service.v1.UnsafePushReq
	7,  // 6: api.service.v1.interface.PushWebhook:input_type -> api.service.v1.PushWebhookReq
	9,  // 7: api.service.v1.interface.Benchmark:input_type -> api.service.v1.BenchmarkReq
	0,  // 8: api.service.v1.interface.Health:output_type -> api.service.v1.CommonRes
	0,  // 9: api.service.v1.interface.DevicePushTokenReport:output_type -> api.service.v1.CommonRes
	0,  // 10: api.service.v1.interface.ClientEvent:output_type -> api.service.v1.CommonRes
	0,  // 11: api.service.v1.interface.UnsafePush:output_type -> api.service.v1.CommonRes
	0,  // 12: api.service.v1.interface.PushWebhook:output_type -> api.service.v1.CommonRes
	0,  // 13: api.service.v1.interface.Benchmark:output_type -> api.service.v1.CommonRes
	8,  // [8:14] is the sub-list for method output_type
	2,  // [2:8] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_api_interface_service_v1_index_proto_init() }
func file_api_interface_service_v1_index_proto_init() {
	if File_api_interface_service_v1_index_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_interface_service_v1_index_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*CommonRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_interface_service_v1_index_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*ExampleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_interface_service_v1_index_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*ExampleRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_interface_service_v1_index_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*YourExampleMainData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_interface_service_v1_index_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*TestRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_interface_service_v1_index_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*DevicePushTokenReportReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_interface_service_v1_index_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*ClientEventReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_interface_service_v1_index_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*PushWebhookReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_interface_service_v1_index_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*UnsafePushReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_interface_service_v1_index_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*BenchmarkReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_interface_service_v1_index_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*CommonRes_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_interface_service_v1_index_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_interface_service_v1_index_proto_goTypes,
		DependencyIndexes: file_api_interface_service_v1_index_proto_depIdxs,
		MessageInfos:      file_api_interface_service_v1_index_proto_msgTypes,
	}.Build()
	File_api_interface_service_v1_index_proto = out.File
	file_api_interface_service_v1_index_proto_rawDesc = nil
	file_api_interface_service_v1_index_proto_goTypes = nil
	file_api_interface_service_v1_index_proto_depIdxs = nil
}
