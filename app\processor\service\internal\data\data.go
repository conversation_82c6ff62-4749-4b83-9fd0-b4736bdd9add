package data

import (
	"sdk-push/app/processor/service/internal/conf"
	"sdk-push/third_party/ustring"
	"sdk-push/third_party/utils"
	"sdk-push/third_party/yk-push-sdk/push"

	"github.com/hibiken/asynq"

	"github.com/go-kratos/kratos/contrib/registry/consul/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/registry"
	"github.com/go-redis/redis/v8"
	"github.com/google/wire"
	consulAPI "github.com/hashicorp/consul/api"
	reqs "github.com/imroc/req"
	"gorm.io/gorm"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(
	NewDiscovery,
	NewRedis,
	NewDB,
	NewData,
	NewHttpClient,
	NewCommonRepo,
	NewProcRepo,
)

// Data .
type Data struct {
	log      *log.Helper
	httpCli  *reqs.Req
	db       *gorm.DB
	rdb      redis.UniversalClient
	uidCli   *ustring.SonyFlake
	msgIdCli *ustring.MsgIDGenerator
	pushCli  *push.RegisterClient
	cfg      *conf.Bootstrap
	producer *asynq.Client
}

// NewData .
func NewData(cnf *conf.Bootstrap, logger log.Logger, cli *reqs.Req, db *gorm.DB, rdbs []redis.UniversalClient) (*Data, func()) {
	var (
		l          = log.NewHelper(log.With(logger, "module", "common/data"))
		produceCnf = asynq.RedisClientOpt{Addr: cnf.Data.Redis.Addrs[0]}
		d          = &Data{
			log:      l,
			httpCli:  cli,
			uidCli:   ustring.NewUniqueIDGenerator(),
			msgIdCli: ustring.NewMsgIDGenerator(),
			db:       db,
			rdb:      rdbs[0],
			cfg:      cnf,
		}
	)
	utils.ExplainCurrentDir()
	var err error
	d.pushCli, err = push.NewRegisterClient(cnf.Push.Dir, "setting.json")
	if err != nil {
		panic(err)
	}
	if len(cnf.Data.Redis.Password) > 0 {
		produceCnf.Password = cnf.Data.Redis.Password
	}
	d.producer = asynq.NewClient(produceCnf)
	return d, func() {
		if err := d.rdb.Close(); err != nil {
			d.log.Error(err)
		}
	}
}

func NewDiscovery(cnf *conf.Bootstrap) registry.Discovery {
	//consul
	if cnf.GetConsul() == nil {
		panic("consul config is nil")
	}
	c := consulAPI.DefaultConfig()
	c.Address = cnf.Consul.GetAddress()
	c.Scheme = cnf.Consul.GetScheme()
	cli, err := consulAPI.NewClient(c)
	if err != nil {
		panic(err)
	}
	r := consul.New(cli, consul.WithHealthCheck(true))
	return r
}

// func NewDataSinkerGrpcClient(config *conf.Bootstrap, r registry.Discovery, tp *tracesdk.TracerProvider) sinkerV1.DataSinkerClient {
// 	conn, errs := grpc.DialInsecure(
// 		context.Background(),
// 		grpc.WithEndpoint("discovery:///"+config.GrpcClient.GetSinker()),
// 		grpc.WithDiscovery(r),
// 		grpc.WithMiddleware(
// 			tracing.Client(tracing.WithTracerProvider(tp)),
// 			recovery.Recovery(),
// 			metrics.Client(
// 				metrics.WithSeconds(conf.MetricGrpcClientSeconds),
// 				metrics.WithRequests(conf.MetricGrpcClientRequests),
// 			),
// 		),
// 		grpc.WithTimeout(config.Server.Grpc.Timeout.AsDuration()),
// 		grpc.WithHealthCheck(false),
// 	)
// 	if errs != nil {
// 		panic(errs)
// 	}
// 	return sinkerV1.NewDataSinkerClient(conn)
// }
