syntax = "proto3";

package api.service.v1;

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "validate/validate.proto";
import "google/protobuf/empty.proto";
// import "google/protobuf/any.proto";


option go_package = "sdk-push/interface/service/v1;v1";
service interface {
  rpc Health(google.protobuf.Empty) returns (CommonRes){
    option (google.api.http) = {
      post: "/health"
      body: "*"
      additional_bindings: {
        get: "/health"
      }
    };
  };

  rpc DevicePushTokenReport(DevicePushTokenReportReq) returns (CommonRes) {
    option (google.api.http) = {
      post: "/api/v1/report"
      body: "*"
    };
  };
  rpc ClientEvent(ClientEventReq) returns (CommonRes) {
    option (google.api.http) = {
      post: "/api/v1/event"
      body: "*"
    };
  };
  rpc UnsafePush(UnsafePushReq) returns (CommonRes) {
    option (google.api.http) = {
      post: "/api/v1/unsafe_push"
      body: "*"
    };
  }
  rpc PushWebhook(PushWebhookReq) returns (CommonRes) {
    option (google.api.http) = {
      post: "/api/v1/push_webhook/{id}/{platform}"
      body: "*"
      additional_bindings: {
        get: "/api/v1/push_webhook/{id}/{platform}"
      }
    };
  };
  rpc Benchmark(BenchmarkReq) returns (CommonRes) {
    option (google.api.http) = {
      post: "/api/v1/benchmark"
      body: "*"
    };
  }
}
// 公共响应体
message CommonRes{
  int32 code = 1;
  message Data{
    bool is_ok = 1;
  }
  Data data = 2;
}
// 具体字段对外使用下划线json格式 在golang技术栈内部会自动生成驼峰struct 迁移过来的老协议考虑兼容 不遵循该规则
message ExampleReq {
  int32 app_id = 1 [(validate.rules).int32.gt = 0, (google.api.field_behavior) = REQUIRED];
  int64 channel_id = 2;
  string sign = 3;
}

message ExampleRes{
  // code
  int32 code = 1;
  // data
  YourExampleMainData data = 2;
}

message YourExampleMainData{
  string some_data = 1;
}

message TestRes{
  string id = 1;
}
// enum Plartform {
//   UNSPECIFIED = 0; // 无含义
//   IOS = 1; // iOS-官方推送
//   HUAWEI = 2; // 华为-官方推送
//   HONOR = 3; // 荣耀-官方推送
//   XIAOMI = 4; // 小米-官方推送
//   OPPO = 5; // OPPO-官方推送
//   VIVO = 6; // vivo-官方推送
//   MEIZU = 7; // 魅族-官方推送
//   JPUSH = 8; // 三方推送-极光平台（自适应全手机平台）
// }
message DevicePushTokenReportReq {
  string app_id = 1 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // 应用id
  string channel_id = 2 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // 渠道id
  string user_id = 3 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // 用户id
  string token = 4 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // 设备推送token
  string device_id = 5 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // 设备id 
  string s_oneid = 6 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // 设备指纹id
  string bundle_id = 7 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // 包名
  int32 platform = 8 [(validate.rules).int32={in: [1,2,3,4,5,6,7,8]},(google.api.field_behavior) = REQUIRED]; // 推送平台类型 1:ios-官推 2:华为-官推 3:荣耀-官推 4:小米-官推 5:oppo-官推 6:vivo-官推 7:魅族-官推 8:三方推送-极光平台（自适应全手机平台）
  string sign = 9 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // 签名
}

message ClientEventReq {
  string app_id = 1 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // 应用id
  string channel_id = 2 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // 渠道id
  string user_id = 3 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // 用户id
  string event_id = 4 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // 魔方的事件id
  string device_id = 5 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // 设备id 
  uint64 event_time = 6 [(validate.rules).uint64.gt = 0, (google.api.field_behavior) = REQUIRED]; // 事件触发的时间 秒级时间戳
  string msg_id = 7 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // 消息id 通过消息通知透传字段获取
  int32 platform = 8 [(validate.rules).int32={in: [1,2,3,4,5,6,7,8]},(google.api.field_behavior) = REQUIRED]; // 推送平台类型 1:ios-官推 2:华为-官推 3:荣耀-官推 4:小米-官推 5:oppo-官推 6:vivo-官推 7:魅族-官推 8:三方推送-极光平台（自适应全手机平台）
  string extra_json = 9; // 可选 额外的json字符串数据 移动端需要将数据序列化为字符串再赋值给该字段
  string sign = 10 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // 签名
}

message PushWebhookReq {
  int64 id = 1;
  int32 platform = 2;
}

message UnsafePushReq {
  string app_id = 1 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED];
  string channel_id = 2 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED];
  string device_id = 3 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED];
  int32 platform = 4 [(validate.rules).int32.gt = 0, (google.api.field_behavior) = REQUIRED];
  string title = 5 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED];
  string content = 6 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED];
  string token = 7 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // 推送要用的token 需要是推送平台存储过的
  string sign = 8 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // 签名
}

message BenchmarkReq {
  string app_id = 1 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // app_id 与 签名要匹配
  string sign = 2[(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // 签名
}