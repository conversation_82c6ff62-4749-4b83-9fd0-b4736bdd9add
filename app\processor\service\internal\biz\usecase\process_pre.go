package usecase

import (
	"context"
	"sdk-push/model"
	"time"
)

func (u *Usecase) HandleMsgPreCalculation(ctx context.Context, payload *model.MsgPreCalculatePayload) (err error) {
	logCtx := u.log.WithContext(ctx)
	logCtx.Infof("[HandleMsgPreCalculation] Processing msg pre calculation for ID: %d", payload.Data.Id)

	// 1. 获取推送消息记录及其关联的人群包
	record, err := u.procRepo.GetPushMessageRecordWithGroup(ctx, payload.Data.Id)
	if err != nil {
		logCtx.Errorf("[HandleMsgPreCalculation] Failed to get push message record: %v", err)
		return err
	}

	if record == nil {
		logCtx.Warnf("[HandleMsgPreCalculation] Push message record not found with ID: %d", payload.Data.Id)
		return nil
	}
	shouldSend := false
	// 如果是延时任务 并且 时间小于10秒 则直接发送
	if record.PushStrategy == model.PushStrategyTim {
		if time.Until(*record.ScheduledTime).Seconds() < 10 {
			shouldSend = true
		}
	}
	err = u.handleMsg(ctx, record, shouldSend)
	if err != nil {
		return
	}
	// delay = record.ScheduledTime.Sub(time.Now().In(loc))
	// 如果发送时间还很久 那就放入延时任务中
	if !shouldSend {
		err = u.commonRepo.Produce(ctx, payload, time.Until(*record.ScheduledTime))
	}
	return
}
