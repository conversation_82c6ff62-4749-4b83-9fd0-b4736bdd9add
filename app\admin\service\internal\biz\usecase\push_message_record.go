package usecase

import (
	"context"
	"errors"
	"fmt"
	pb "sdk-push/api/admin/service/v1"
	errv1 "sdk-push/api/errs/v1"
	"sdk-push/app/admin/service/internal/biz/repository"
	"sdk-push/model"
	"sdk-push/third_party/utils"
	"sort"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

func (uc *Usecase) CreatePushMessageRecord(ctx context.Context, req *pb.CreatePushMessageRecordRequest) error {
	record := &model.PushMessageRecord{
		PushTitle:       req.PushTitle,
		PushContent:     req.PushContent,
		PushPlatform:    req.PushPlatform,
		PushStrategy:    req.PushStrategy,
		PushRange:       req.PushRange,
		PushStatus:      model.PushStatusWaiting,
		IosSandbox:      req.IosSandbox,
		PushSystem:      req.PushSystem,
		Operator:        utils.GetCurrentAdminNickname(ctx),
		AppId:           req.AppId,
		AndroidRightPic: req.AndroidRightPic,
		AndroidBigPic:   req.AndroidBigPic,
		IOSBigPic:       req.IosBigPic,
	}

	loc, _ := time.LoadLocation("Asia/Shanghai")
	if req.PushStrategy == model.PushStrategyTim {
		scheduledTime, err := time.ParseInLocation(time.DateTime, req.ScheduledTime, loc)
		if err != nil {
			uc.log.WithContext(ctx).Errorf("parse scheduled time failed: %v", err)
			return err
		}
		record.ScheduledTime = &scheduledTime
	} else if req.PushStrategy == model.PushStrategyNow {
		scheduledTime := time.Now().In(loc)
		record.ScheduledTime = &scheduledTime
	}

	var (
		userGroup *model.PushUserGroup
		err       error
	)
	if req.PushRange == model.PushRangeTag {
		record.TargetGroupId = req.TargetGroupId
		userGroup, err = uc.adminRepo.GetPushUserGroup(ctx, req.TargetGroupId)
		if err != nil {
			uc.log.WithContext(ctx).Errorf("get push user group failed: %v", err)
			return err
		}
		if userGroup == nil {
			return errv1.ErrorValidator("选择的人群包不存在")
		}
		if userGroup.GroupUploadStatus != model.PushUserGroupStatusSuccess || userGroup.SuccessFilepath == "" {
			return errv1.ErrorValidator("当前人群包未解析成功不允许推送")
		}
	} else if req.PushRange == model.PushRangeToken {
		record.PushUserTokens = req.PushUserTokens
	} else {
		// 查询用户是否存在token
		var system []int
		for _, v := range strings.Split(req.GetPushSystem(), ",") {
			val, _ := strconv.Atoi(v)
			system = append(system, val)
		}
		_, err = uc.adminRepo.QueryUserDeviceTokenByCondition(ctx, req.AppId, system, req.PushUserId)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return errv1.ErrorValidator("指定用户未查询到设备token")
			}
			return err
		}
		record.PushUserId = req.PushUserId
	}

	if err = uc.adminRepo.CreatePushMessageRecord(ctx, record); err != nil {
		uc.log.WithContext(ctx).Errorf("create push message record failed: %v", err)
		return err
	}

	// 创建推送任务
	msgSendPayload := new(model.MsgSendPayload)
	msgSendPayload.Data.Id = int64(record.Id)

	if req.PushStrategy == model.PushStrategyTim {
		// 如果是延时任务则创建消息预计算任务 任务会自行发消息
		msgPreCalculatePayload := new(model.MsgPreCalculatePayload)
		msgPreCalculatePayload.Data.Id = int64(record.Id)

		uc.log.WithContext(ctx).Infow("msg", "produce msg pre calculate", "payload", msgPreCalculatePayload)

		err = uc.commonRepo.Produce(ctx, msgPreCalculatePayload, time.Duration(0))
		if err != nil {
			uc.log.WithContext(ctx).Errorf("produce msg pre calculate payload failed: %v", err)
			return err
		}
		return nil // 这里中断 计算任务处理完会同步自行处理延时任务
	}

	uc.log.WithContext(ctx).Infow("msg", "produce msg send", "payload", msgSendPayload)

	err = uc.commonRepo.Produce(ctx, msgSendPayload, time.Duration(0))
	if err != nil {
		uc.log.WithContext(ctx).Errorf("produce msg send payload failed: %v", err)
	}

	return nil
}

func (uc *Usecase) DeletePushMessageRecord(ctx context.Context, req *pb.DeletePushMessageRecordRequest) error {
	record, err := uc.adminRepo.GetPushMessageRecord(ctx, req.Id)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("get push message record failed: %v", err)
		return err
	}

	if record == nil {
		return errv1.ErrorValidator("推送记录不存在")
	}

	return uc.adminRepo.DeletePushMessageRecord(ctx, req.Id)
}

func (uc *Usecase) GetPushMessageRecord(ctx context.Context, req *pb.GetPushMessageRecordRequest) (*pb.PushMessageRecordDetail, error) {
	record, err := uc.adminRepo.GetPushMessageRecord(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if record == nil {
		return nil, errv1.ErrorValidator("推送记录不存在")
	}

	return &pb.PushMessageRecordDetail{
		Data: &pb.PushMessageRecord{
			Id:             record.Id,
			PushTitle:      record.PushTitle,
			PushContent:    record.PushContent,
			PushPlatform:   record.PushPlatform,
			PushStrategy:   record.PushStrategy,
			PushRange:      record.PushRange,
			TargetGroupId:  record.TargetGroupId,
			PushStatus:     record.PushStatus,
			CreatedAt:      record.CreatedAt.Format(time.DateTime),
			UpdatedAt:      record.UpdatedAt.Format(time.DateTime),
			PushSystem:     record.PushSystem,
			PushUserTokens: record.PushUserTokens,
			ScheduledTime:  record.ScheduledTime.Format(time.DateTime),
			Operator:       record.Operator,
			IosSandbox:     record.IosSandbox,
			AppId:          record.AppId,
		},
	}, nil
}

func (uc *Usecase) CancelPushMessageRecord(ctx context.Context, req *pb.CancelPushMessageRecordRequest) error {
	record, err := uc.adminRepo.GetPushMessageRecord(ctx, req.Id)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("get push message record failed: %v", err)
		return err
	}

	if record == nil {
		return errv1.ErrorValidator("推送记录不存在")
	}

	if record.PushStatus == model.PushStatusCanceled {
		return errv1.ErrorValidator("当前状态已取消")
	}

	if record.PushStatus == model.PushStatusSuccess {
		return errv1.ErrorValidator("当前状态已成功")
	}

	return uc.adminRepo.UpdatePushMessageRecordStatus(ctx, req.Id, model.PushStatusCanceled)
}

func (uc *Usecase) ListPushMessageRecord(ctx context.Context, req *pb.ListPushMessageRecordRequest) (*pb.ListPushMessageRecordReply, error) {
	filter := repository.ListPushMessageRecordDTO{
		Page:       req.Page,
		PageSize:   req.PageSize,
		Search:     req.SearchText,
		Platform:   req.PushPlatform,
		PushStatus: req.PushStatus,
		AppId:      req.AppId,
	}

	records, total, err := uc.adminRepo.ListPushMessageRecord(ctx, &filter)
	if err != nil {
		uc.log.WithContext(ctx).Errorf("list push message record failed: %v", err)
		return nil, err
	}

	items := make([]*pb.PushMessageRecord, 0, len(records))
	for _, record := range records {
		scheduledTime := ""
		if record.ScheduledTime != nil {
			scheduledTime = record.ScheduledTime.Format(time.DateTime)
		}
		var channelDetail []*pb.PushMessageRecord_ChannelDetail
		var userCount uint64
		var deviceCount uint64
		for _, v := range record.PushSystemStatistics {
			channelDetail = append(channelDetail, &pb.PushMessageRecord_ChannelDetail{
				PushChannel:     int32(v.Platform),
				PushChannelIcon: model.PlatformIcon[v.Platform],
				UserCount:       uint64(v.UserCount),
				DeviceCount:     uint64(v.TokenCount),
				SentCount:       uint64(v.SentCount),
				SentRate:        fmt.Sprintf("%.2f%%", v.SentRate),
				ArrivalCount:    uint64(v.ArrivalCount),
				ArrivalRate:     fmt.Sprintf("%.2f%%", v.ArrivalRate),
				DisplayCount:    uint64(v.DisplayCount),
				DisplayRate:     fmt.Sprintf("%.2f%%", v.DisplayRate),
				ClickCount:      uint64(v.ClickCount),
				ClickRate:       fmt.Sprintf("%.2f%%", v.ClickRate),
				LaunchCount:     uint64(v.LaunchCount),
				LaunchRate:      fmt.Sprintf("%.2f%%", v.LaunchRate),
				PushStatus:      int32(v.PushStatus),
			})
			userCount += uint64(v.UserCount)
			deviceCount += uint64(v.TokenCount)
		}
		sort.Slice(channelDetail, func(i, j int) bool {
			return model.PlatformIconDisplaySort[int8(channelDetail[i].PushChannel)] > model.PlatformIconDisplaySort[int8(channelDetail[j].PushChannel)]
		})
		items = append(items, &pb.PushMessageRecord{
			Id:              record.Id,
			PushTitle:       record.PushTitle,
			PushContent:     record.PushContent,
			PushPlatform:    record.PushPlatform,
			PushStrategy:    record.PushStrategy,
			PushRange:       record.PushRange,
			TargetGroupId:   record.TargetGroupId,
			PushStatus:      record.PushStatus,
			CreatedAt:       record.CreatedAt.Format(time.DateTime),
			UpdatedAt:       record.UpdatedAt.Format(time.DateTime),
			PushSystem:      record.PushSystem,
			PushUserTokens:  record.PushUserTokens,
			ScheduledTime:   scheduledTime,
			Operator:        record.Operator,
			IosSandbox:      record.IosSandbox,
			AppId:           record.AppId,
			AndroidRightPic: record.AndroidRightPic,
			AndroidBigPic:   record.AndroidBigPic,
			IosBigPic:       record.IOSBigPic,
			PushUserId:      record.PushUserId,
			UserCount:       userCount,
			DeviceCount:     deviceCount,
			ChannelDetail:   channelDetail,
		})
	}

	return &pb.ListPushMessageRecordReply{
		Data: &pb.ListPushMessageRecordReply_Data{
			Total: uint64(total),
			List:  items,
		},
	}, nil
}
